CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(40) PRIMARY KEY,
    email VARCHAR(254) NOT NULL UNIQUE,    
    fname VA<PERSON>HAR(30) NOT NULL,
    lname VA<PERSON>HAR(30) NOT NULL,
    dob DATE NOT NULL,
    imgurl VARCHAR(255),
    nickname <PERSON><PERSON><PERSON><PERSON>(30),
    about TEXT,
    password VARCHAR(255) NOT NULL,
    profileVisibility TEXT NOT NULL DEFAULT 'public' CHECK(profileVisibility IN ('public', 'private')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
